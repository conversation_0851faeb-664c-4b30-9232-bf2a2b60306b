#include "gd32f470vet6.h"





extern rtc_parameter_struct rtc_initpara;

/*!
    \brief      display the current time
    \param[in]  none
    \param[out] none
    \retval     none
*/


int rtc_set_time(uint8_t hour, uint8_t minute, uint8_t second)//设置时间
{
    /* 使用当前日期，只更新时间 */
    rtc_current_time_get(&rtc_initpara);
    
    /* 设置新的时间值 */
    rtc_initpara.hour = hour;
    rtc_initpara.minute = minute;
    rtc_initpara.second = second;
    
    /* 禁用写保护 */
    RTC_WPK = RTC_UNLOCK_KEY1;
    RTC_WPK = RTC_UNLOCK_KEY2;
    
    /* 进入初始化模式 */
    if(ERROR == rtc_init_mode_enter()) {
        /* 启用写保护 */
        RTC_WPK = RTC_LOCK_KEY;
        return -1;
    }
    
    /* 更新RTC时间 */
    if(ERROR == rtc_init(&rtc_initpara)) {
        /* 启用写保护 */
        RTC_WPK = RTC_LOCK_KEY;
        return -1;
    }
    
    /* 退出初始化模式 */
    rtc_init_mode_exit();
    
    /* 等待寄存器同步 */
    rtc_register_sync_wait();
    
    /* 启用写保护 */
    RTC_WPK = RTC_LOCK_KEY;
    
    return 0;
}

int rtc_set_date(uint8_t year, uint8_t month, uint8_t date, uint8_t day_of_week)
{
    /* 使用当前时间，只更新日期 */
    rtc_current_time_get(&rtc_initpara);
    
    /* 设置新的日期值 */
    rtc_initpara.year = year;
    rtc_initpara.month = month;
    rtc_initpara.date = date;
    rtc_initpara.day_of_week = day_of_week;
    
    /* 禁用写保护 */
    RTC_WPK = RTC_UNLOCK_KEY1;
    RTC_WPK = RTC_UNLOCK_KEY2;
    
    /* 进入初始化模式 */
    if(ERROR == rtc_init_mode_enter()) {
        /* 启用写保护 */
        RTC_WPK = RTC_LOCK_KEY;
        return -1;
    }
    
    /* 更新RTC日期 */
    if(ERROR == rtc_init(&rtc_initpara)) {
        /* 启用写保护 */
        RTC_WPK = RTC_LOCK_KEY;
        return -1;
    }
    
    /* 退出初始化模式 */
    rtc_init_mode_exit();
    
    /* 等待寄存器同步 */
    rtc_register_sync_wait();
    
    /* 启用写保护 */
    RTC_WPK = RTC_LOCK_KEY;
    
    return 0;
}

char time_flag=0;


void time_set()
{
if(time_flag==0)
{
	// 设置时间为12:34:56 (注意使用BCD格式)
rtc_set_time(0x12, 0x34, 0x56);

// 设置日期为2024年5月15日，星期三 (注意年月日使用BCD格式)
rtc_set_date(0x24, RTC_MAY, 0x15, RTC_WEDSDAY);
time_flag=1;
}
}



void rtc_task(void)
{
		time_set();
    rtc_current_time_get(&rtc_initpara);

    //oled_printf(0, 3, "%0.2x:%0.2x:%0.2x", rtc_initpara.hour, rtc_initpara.minute, rtc_initpara.second);
}





