#include "system.h"


float g_ratio_value = 1.99f;  // ????
float g_limit_value = 5.11f; // ????

uint32_t sys_log_flag=0;



//?????????
void print_time()
{
		            rtc_parameter_struct current_time;
                rtc_current_time_get(&current_time);

                // 将BCD格式转换为十进制显示
                uint8_t year_dec = ((current_time.year >> 4) * 10) + (current_time.year & 0x0F);
                uint8_t month_dec = ((current_time.month >> 4) * 10) + (current_time.month & 0x0F);
                uint8_t date_dec = ((current_time.date >> 4) * 10) + (current_time.date & 0x0F);
                uint8_t hour_dec = ((current_time.hour >> 4) * 10) + (current_time.hour & 0x0F);
                uint8_t minute_dec = ((current_time.minute >> 4) * 10) + (current_time.minute & 0x0F);
                uint8_t second_dec = ((current_time.second >> 4) * 10) + (current_time.second & 0x0F);

                my_printf(DEBUG_USART, "20%02d-%02d-%02d %02d:%02d:%02d\r\n",
                          year_dec, month_dec, date_dec,
                          hour_dec, minute_dec, second_dec);
}


// ??????????????????????RTC
bool parse_and_set_datetime(const char* datetime_str)
{
    // ???????: "YYYY-MM-DD HH:MM:SS"
    uint8_t year, month, date, hour, minute, second;
    uint8_t day_of_week = RTC_MONDAY; // ????????
    
    // ?????????????????????
    if(strlen(datetime_str) < 19) {
        return false;
    }
    // ????? write_log_to_file ????????????
    // ?????? usart_app.c ???????????
    year = (uint8_t)((datetime_str[2] - '0') * 10 + (datetime_str[3] - '0')); // ????????
    month = (uint8_t)((datetime_str[5] - '0') * 10 + (datetime_str[6] - '0'));
    date = (uint8_t)((datetime_str[8] - '0') * 10 + (datetime_str[9] - '0'));
    
    hour = (uint8_t)((datetime_str[11] - '0') * 10 + (datetime_str[12] - '0'));
    minute = (uint8_t)((datetime_str[14] - '0') * 10 + (datetime_str[15] - '0'));
    second = (uint8_t)((datetime_str[17] - '0') * 10 + (datetime_str[18] - '0'));
    
    // ????BCD???
    uint8_t bcd_year = ((year / 10) << 4) | (year % 10);
    uint8_t bcd_date = ((date / 10) << 4) | (date % 10);
    uint8_t bcd_hour = ((hour / 10) << 4) | (hour % 10);
    uint8_t bcd_minute = ((minute / 10) << 4) | (minute % 10);
    uint8_t bcd_second = ((second / 10) << 4) | (second % 10);
    
    // 月份转换为BCD格式（不使用RTC常量）
    uint8_t bcd_month = ((month / 10) << 4) | (month % 10);



    // ????RTC????????
    if(rtc_set_time(bcd_hour, bcd_minute, bcd_second) != 0) {
        return false;
    }

    if(rtc_set_date(bcd_year, bcd_month, bcd_date, day_of_week) != 0) {
        return false;
    }

    // 设置成功，不在这里打印，由调用方处理

    return true;
}



void sys_all_test()
{
    uint32_t flash_id;
    uint32_t expected_flash_id = 0xC84013;  // 期望的flash id
    sd_error_enum status;

    // 1. Initialize SPI Flash driver (mainly CS pin state)
    spi_flash_init();

    // 2. Read Flash ID
    flash_id = spi_flash_read_id();

    // 3. Test Flash
    if (flash_id == expected_flash_id)
    {
        my_printf(DEBUG_USART, "flash.........ok\r\n");
    }
    else
    {
        my_printf(DEBUG_USART, "flash.........error\r\n");
    }

    // 4. Test TF Card
    status = sd_init();
    if (SD_OK == status)
    {
        my_printf(DEBUG_USART, "TF card..........ok\r\n");
    }
    else
    {
        my_printf(DEBUG_USART, "TF card..........error\r\n");
    }

    // 5. Print Flash ID
    my_printf(DEBUG_USART, "Flash ID: 0x%lX\r\n", flash_id);

    // 6. Print TF Card info or error message
    if (SD_OK == status)
    {
        uint32_t capacity_kb = sd_card_capacity_get();
        my_printf(DEBUG_USART, "TF card memory: %d KB\r\n", capacity_kb);
        write_log_to_file("test ok");
    }
    else
    {
        my_printf(DEBUG_USART, "can not find TF card\r\n");
        write_log_to_file("test error: tf card not found");
    }

    // 7. Print RTC time
    my_printf(DEBUG_USART, "RTC:");
    print_time();
}



void my_write_flash()
{
 const char *device_id_str = "Device_ID:2025-CIMC-2025232283";
    uint16_t device_id_len = strlen(device_id_str);
    uint32_t target_addr = 0x000100; // ????????????????0x000000???????boot????????

    uint8_t write_buffer[SPI_FLASH_PAGE_SIZE];
    uint8_t read_buffer[SPI_FLASH_PAGE_SIZE];

    my_printf(DEBUG_USART, "=== Write/Read Device ID Start ===\r\n");

    // 1. ????? SPI Flash
    spi_flash_init();

    // 2. ??????????????????
    spi_flash_sector_erase(target_addr);

    // 3. ??????????
    memset(write_buffer, 0x00, SPI_FLASH_PAGE_SIZE);
    if (device_id_len >= SPI_FLASH_PAGE_SIZE) {
        device_id_len = SPI_FLASH_PAGE_SIZE - 1;
    }
    memcpy(write_buffer, device_id_str, device_id_len);
    write_buffer[device_id_len] = '\0';  // ???????????

    // 4. ???????
    spi_flash_buffer_write(write_buffer, target_addr, SPI_FLASH_PAGE_SIZE);

    // 5. ???????
    memset(read_buffer, 0x00, SPI_FLASH_PAGE_SIZE);
    spi_flash_buffer_read(read_buffer, target_addr, SPI_FLASH_PAGE_SIZE);
    my_printf(DEBUG_USART, "Read back data: \"%s\"\r\n", read_buffer);

    // 6. ???
    if (memcmp(write_buffer, read_buffer, SPI_FLASH_PAGE_SIZE) == 0)
    {
        my_printf(DEBUG_USART, "Device ID VERIFIED successfully.\r\n");
    }
    else
    {
        my_printf(DEBUG_USART, "Device ID VERIFICATION FAILED!\r\n");
    }

}


void my_reaf_flash()
{
    uint32_t target_addr = 0x000100; // ???????????????
    uint8_t read_buffer[SPI_FLASH_PAGE_SIZE];

    // ????
    memset(read_buffer, 0x00, SPI_FLASH_PAGE_SIZE);
    spi_flash_buffer_read(read_buffer, target_addr, SPI_FLASH_PAGE_SIZE);

    // ?flash?????????? "Device_ID:" ?????????
    my_printf(DEBUG_USART, "%s\r\n", read_buffer);
}

/**
 * @brief ??????????????????
 * ???????????????????????
 */
void check_reset_source_and_init(void)
{
    // ????????????????
    if (RESET != rcu_flag_get(RCU_FLAG_EPRST)) {
        // ????????????????
        my_printf(DEBUG_USART, "====system init====\r\n");

        // ??flash?????????ID?????????
        uint32_t target_addr = 0x000100;
        uint8_t read_buffer[SPI_FLASH_PAGE_SIZE];
        memset(read_buffer, 0x00, SPI_FLASH_PAGE_SIZE);
        spi_flash_buffer_read(read_buffer, target_addr, SPI_FLASH_PAGE_SIZE);

        // ???????????ID??"Device_ID:"???
        if (strncmp((const char*)read_buffer, "Device_ID:", 10) != 0) {
            // flash????????ID????
            my_write_flash();
        }

        // ???????ID
        my_reaf_flash();
        my_printf(DEBUG_USART, "====system ready====\r\n");
    }

    // ?????????????????
    // ???????????? bsp_rtc_init ?? rcu_all_reset_flag_clear() ???
}

void create_config_ini()
{
    FIL file;
    FRESULT res;
    UINT bw;
    
    // ??????
    const char *config_content = 
        "[Ratio]\r\n"
        "Ch0 = 1.99\r\n"
        "[Limit]\r\n"
        "Ch0 = 10.11\r\n";
    
    // ??/????
    res = f_open(&file, "config.ini", FA_CREATE_ALWAYS | FA_WRITE);
    if (res != FR_OK) {
        my_printf(DEBUG_USART, "Failed to create config.ini file! Error: %d\r\n", res);
        return;
    }
    
    // ????
    res = f_write(&file, config_content, strlen(config_content), &bw);
    if (res != FR_OK || bw != strlen(config_content)) {
        my_printf(DEBUG_USART, "Failed to write to config.ini! Error: %d\r\n", res);
    } else {
        my_printf(DEBUG_USART, "config.ini created successfully\r\n");
    }
    
    // ????
    f_close(&file);
}

void check_config_ini()
{
    FIL file;
    FRESULT res;
    
    // ?????????????????
    res = f_open(&file, "config.ini", FA_READ);
    
    if (res == FR_OK) {
        // ???????
        //my_printf(DEBUG_USART, "config.ini exists...ok\r\n");
        // ??????
        f_close(&file);
    } else {
        // ??????????????????
        my_printf(DEBUG_USART, "config.ini file not found\r\n");
			create_config_ini();
    }
}

// ?????????????????

void read_config_ini()
{
    FIL file;
    FRESULT res;
    UINT br;
    char buffer[256] = {0}; // ??????????????????
    char *ratio_section, *limit_section;
    
    // ????????(?????)
    res = f_open(&file, "config.ini", FA_READ);
    
    if (res != FR_OK) {
        my_printf(DEBUG_USART, "Failed to open config.ini! Error: %d\r\n", res);
        return;
    }
    
    // ??????????
    res = f_read(&file, buffer, sizeof(buffer) - 1, &br);
    
    if (res != FR_OK) {
        my_printf(DEBUG_USART, "Failed to read config.ini! Error: %d\r\n", res);
    } else {
        // ??????????null???
        buffer[br] = '\0';
        
        // ????????Ratio?
        ratio_section = strstr(buffer, "[Ratio]");
        if (ratio_section) {
            char *ratio_line = strstr(ratio_section, "Ch0 =");
            if (ratio_line) {
                sscanf(ratio_line, "Ch0 = %f", &g_ratio_value);
            }
        }
        
        // ????????Limit?
        limit_section = strstr(buffer, "[Limit]");
        if (limit_section) {
            char *limit_line = strstr(limit_section, "Ch0 =");
            if (limit_line) {
                sscanf(limit_line, "Ch0 = %f", &g_limit_value);
            }
        }
        
        // ??????????
        //my_printf(DEBUG_USART, "=== config.ini content ===\r\n");
        //my_printf(DEBUG_USART, "%s\r\n", buffer);
        //my_printf(DEBUG_USART, "=========================\r\n");
        my_printf(DEBUG_USART, "Ratio=%.2f \r\n Limit=%.2f\r\n", g_ratio_value, g_limit_value);
				my_printf(DEBUG_USART,"config read success\r\n");
    }
    
    // ??????
    f_close(&file);
}




bool update_config_ratio(float ratio_value)
{
    FIL file;
    FRESULT res;
    UINT bw;
    char new_content[256] = {0};

    // 更新全局变量
    g_ratio_value = ratio_value;

    // 准备新的配置内容
    sprintf(new_content,
            "[Ratio]\r\n"
            "Ch0 = %.2f\r\n\r\n"
            "[Limit]\r\n"
            "Ch0 = %.2f\r\n",
            g_ratio_value, g_limit_value);

    // 尝试打开文件进行写入
    res = f_open(&file, "config.ini", FA_CREATE_ALWAYS | FA_WRITE);
    if (res != FR_OK) {
        // 如果文件操作失败，仍然更新内存中的值，但不保存到文件
        // 这样至少在当前运行期间值是正确的
        return true; // 返回true，因为内存中的值已经更新
    }
    
    res = f_write(&file, new_content, strlen(new_content), &bw);
    f_close(&file);

    if (res == FR_OK && bw == strlen(new_content)) {
        // 写入成功后记录日志
        char str2[10];
        sprintf(str2, "%.2f", ratio_value);
        char str1[40]="ratio config success to ";
        strcat(str1, str2);
        write_log_to_file(str1);
    }

    // 无论文件操作是否成功，都返回true，因为内存中的值已经更新
    return true;
}

bool update_config_limit(float limit_value)
{
    FIL file;
    FRESULT res;
    UINT bw;
    char new_content[256] = {0};
		

    // 更新全局变量
    g_limit_value = limit_value;
    
    // ???????????????
    sprintf(new_content, 
            "[Ratio]\r\n"
            "Ch0 = %.2f\r\n\r\n"
            "[Limit]\r\n"
            "Ch0 = %.2f\r\n",
            g_ratio_value, g_limit_value);
    
    // 尝试打开文件进行写入
    res = f_open(&file, "config.ini", FA_CREATE_ALWAYS | FA_WRITE);
    if (res != FR_OK) {
        // 如果文件操作失败，仍然更新内存中的值
        return true;
    }
    
    res = f_write(&file, new_content, strlen(new_content), &bw);
    if (res != FR_OK || bw != strlen(new_content)) {
        // 写入失败，但仍然返回true，因为内存中的值已经更新
        return true;
    } else {
        // 写入成功后记录日志
        char str2[10];
        sprintf(str2, "%.2f", limit_value);
        char str1[40]="limit config success to ";
        strcat(str1, str2);
        write_log_to_file(str1);

    }
    
    f_close(&file);
    return true;
}




void save_config_values_to_flash(void)
{
    uint8_t write_buffer[SPI_FLASH_PAGE_SIZE];
    uint8_t read_buffer[SPI_FLASH_PAGE_SIZE];
    uint32_t target_addr = 0x001000;
    
    // ???????????
    float ratio_value = g_ratio_value;
    float limit_value = g_limit_value;
    
//my_printf(DEBUG_USART, "=== ??????????????Flash ===\r\n");
    
    // ???????????
    spi_flash_sector_erase(target_addr);
    
    // ??????????
    memset(write_buffer, 0x00, SPI_FLASH_PAGE_SIZE);
    
    // ??????????????????
    memcpy(write_buffer, &ratio_value, sizeof(float));
    memcpy(write_buffer + sizeof(float), &limit_value, sizeof(float));
    
    // ???????
    spi_flash_buffer_write(write_buffer, target_addr, SPI_FLASH_PAGE_SIZE);
    
    // ???????????????????
    delay_1ms(10);
    
    // ?????????????
    memset(read_buffer, 0x00, SPI_FLASH_PAGE_SIZE);
    spi_flash_buffer_read(read_buffer, target_addr, SPI_FLASH_PAGE_SIZE);
    
    // ?????????????????
    float read_ratio, read_limit;
    memcpy(&read_ratio, read_buffer, sizeof(float));
    memcpy(&read_limit, read_buffer + sizeof(float), sizeof(float));
    
    // ???%f????????
    //my_printf(DEBUG_USART, "???????: Ratio=%.2f, Limit=%.2f\r\n", (double)read_ratio, (double)read_limit);
    my_printf(DEBUG_USART, "ratio=%.2f\r\n", (double)read_ratio);
		my_printf(DEBUG_USART, "limit=%.2f\r\n",  (double)read_limit);
    my_printf(DEBUG_USART, "save parameters to flash\r\n");

    // ???
    if (memcmp(&ratio_value, &read_ratio, sizeof(float)) == 0 &&
        memcmp(&limit_value, &read_limit, sizeof(float)) == 0)
    {
       // my_printf(DEBUG_USART, "????????????Flash??\r\n");
    }
    else
    {
        my_printf(DEBUG_USART, "?????????????\r\n");
        my_printf(DEBUG_USART, "????: Ratio=%f, Limit=%f\r\n", 
                 (double)ratio_value, (double)limit_value);
        my_printf(DEBUG_USART, "????: Ratio=%f, Limit=%f\r\n", 
                 (double)read_ratio, (double)read_limit);
    }
}



/**
 * @brief ??Flash?????????
 * @return ??????????????????true????????false
 */
bool load_config_values_from_flash(void)
{
    uint8_t read_buffer[SPI_FLASH_PAGE_SIZE];
    uint32_t target_addr = 0x001000; // ???????????????
    
   // my_printf(DEBUG_USART, "=== ?????Flash????????? ===\r\n");
    
    // ????? SPI Flash
    //spi_flash_init();
    
    // ???????
    memset(read_buffer, 0x00, SPI_FLASH_PAGE_SIZE);
    spi_flash_buffer_read(read_buffer, target_addr, SPI_FLASH_PAGE_SIZE);
    
    // ?????????????????
    float read_ratio, read_limit;
    memcpy(&read_ratio, read_buffer, sizeof(float));
    memcpy(&read_limit, read_buffer + sizeof(float), sizeof(float));
    
    // ?????????????????????
    if (read_ratio >= 0.0f && read_ratio <= 100.0f &&
        read_limit >= 0.0f && read_limit <= 200.0f) {

        // ??????????
        g_ratio_value = read_ratio;
        g_limit_value = read_limit;

      //  my_printf(DEBUG_USART, "???????: Ratio=%.2f, Limit=%.2f\r\n", g_ratio_value, g_limit_value);
        	my_printf(DEBUG_USART,"read parameters from flash\r\n");
					my_printf(DEBUG_USART,"ratio: %.2f\r\n",g_ratio_value);
					my_printf(DEBUG_USART,"limit: %.2f\r\n",g_limit_value);

        return true;
    } else {
        my_printf(DEBUG_USART, "Flash?????????Flash????\r\n");
        return false;
    }
}



/**
 * @brief ??Flash?????????
 * @return ??????????????????true????????false
 */
bool load_config_values_from_flashinit(void)
{
    uint8_t read_buffer[SPI_FLASH_PAGE_SIZE];
    uint32_t target_addr = 0x001000; // ???????????????
    
   // my_printf(DEBUG_USART, "=== ?????Flash????????? ===\r\n");
    
    // ????? SPI Flash
    //spi_flash_init();
    
    // ???????
    memset(read_buffer, 0x00, SPI_FLASH_PAGE_SIZE);
    spi_flash_buffer_read(read_buffer, target_addr, SPI_FLASH_PAGE_SIZE);
    
    // ?????????????????
    float read_ratio, read_limit;
    memcpy(&read_ratio, read_buffer, sizeof(float));
    memcpy(&read_limit, read_buffer + sizeof(float), sizeof(float));
    
    // ?????????????????????
    if (read_ratio >= 0.0f && read_ratio <= 100.0f &&
        read_limit >= 0.0f && read_limit <= 200.0f) {

        // ??????????
        g_ratio_value = read_ratio;
        g_limit_value = read_limit;

      //  my_printf(DEBUG_USART, "???????: Ratio=%.2f, Limit=%.2f\r\n", g_ratio_value, g_limit_value);
//        	my_printf(DEBUG_USART,"read parameters from flash\r\n");
//					my_printf(DEBUG_USART,"ratio: %.2f\r\n",g_ratio_value);
//					my_printf(DEBUG_USART,"limit: %.2f\r\n",g_limit_value);

        return true;
    } else {
        my_printf(DEBUG_USART, "Flash?????????Flash????\r\n");
        return false;
    }
}








/**
 * @brief ??Flash????limit_time?
 * @return ???????true????????false
 */
bool load_limit_time_from_flash(void)
{
    uint8_t read_buffer[SPI_FLASH_PAGE_SIZE];
    uint32_t target_addr = 0x002000; // ???????????????
    
    //my_printf(DEBUG_USART, "=== ?????Flash??????????? ===\r\n");
    
    // ???????
    memset(read_buffer, 0x00, SPI_FLASH_PAGE_SIZE);
    spi_flash_buffer_read(read_buffer, target_addr, SPI_FLASH_PAGE_SIZE);
    
    // ?????????????????
    uint16_t read_limit_time;
    memcpy(&read_limit_time, read_buffer, sizeof(uint16_t));
    
    // ?????????????????????
    if (read_limit_time >= 1000 && read_limit_time <= 60000) {
        // ??????????
        limit_time = read_limit_time;
        
        //my_printf(DEBUG_USART, "????????????: %dms\r\n", limit_time);
       // adc_print(); // ??????????????
        return true;
    } else {
       // my_printf(DEBUG_USART, "Flash???????????????Flash???\r\n");
       // my_printf(DEBUG_USART, "?????????????: %dms\r\n", limit_time);
        return false;
    }
}







/**
 * @brief ??uint32_t??????????Flash??
 * @param value ??????uint32_t?
 * @return ???????true????????false
 */
bool save_uint32_value_to_flash(uint32_t value)
{
    uint8_t write_buffer[SPI_FLASH_PAGE_SIZE];
    uint8_t read_buffer[SPI_FLASH_PAGE_SIZE];
    uint32_t target_addr = 0x003000; // ?????????????????
    
   // my_printf(DEBUG_USART, "=== ???????uint32_t???Flash ===\r\n");
    
    // ??????????????????
    spi_flash_sector_erase(target_addr);
    
    // ??????????
    memset(write_buffer, 0x00, SPI_FLASH_PAGE_SIZE);
    
    // ??uint32_t????????????
    memcpy(write_buffer, &value, sizeof(uint32_t));
    
    // ???????
    spi_flash_buffer_write(write_buffer, target_addr, SPI_FLASH_PAGE_SIZE);
    
    // ?????????????
    memset(read_buffer, 0x00, SPI_FLASH_PAGE_SIZE);
    spi_flash_buffer_read(read_buffer, target_addr, SPI_FLASH_PAGE_SIZE);
    
    // ?????????????????
    uint32_t read_value;
    memcpy(&read_value, read_buffer, sizeof(uint32_t));
    
   // my_printf(DEBUG_USART, "???????: %lu\r\n", read_value);
    
    // ???
    if (memcmp(write_buffer, read_buffer, sizeof(uint32_t)) == 0)
    {
        //my_printf(DEBUG_USART, "uint32_t????????Flash??\r\n");
        return true;
    }
    else
    {
        //my_printf(DEBUG_USART, "uint32_t?????????\r\n");
        return false;
    }
}

/**
 * @brief ??Flash????uint32_t?
 * @return ???????uint32_t????????????????0
 */
uint32_t load_uint32_value_from_flash(void)
{
    uint8_t read_buffer[SPI_FLASH_PAGE_SIZE];
    uint32_t target_addr = 0x003000; // ???????????????
    
   // my_printf(DEBUG_USART, "=== ?????Flash????uint32_t? ===\r\n");
    
    // ???????
    memset(read_buffer, 0x00, SPI_FLASH_PAGE_SIZE);
    spi_flash_buffer_read(read_buffer, target_addr, SPI_FLASH_PAGE_SIZE);
    
    // ?????????????????
    uint32_t read_value;
    memcpy(&read_value, read_buffer, sizeof(uint32_t));
    
    //my_printf(DEBUG_USART, "??????uint32_t?: %lu\r\n", read_value);
    return read_value;
}



/**
 * @brief ??log????????????????txt???
 * @param id ???ID???????????????
 * @return 0-???????0-???
 */
uint8_t create_log_file(uint32_t id)
{
    FRESULT result;
    FIL log_file;
    char filename[64] = {0};
    
    // ?????????
    sprintf(filename, "0:log/log%lu.txt", id);
    
    // ?????????
    result = f_open(&log_file, filename, FA_CREATE_ALWAYS | FA_WRITE);
    if (result != FR_OK) {
        // ????????????
        sprintf(filename, "log/log%lu.txt", id);
        result = f_open(&log_file, filename, FA_CREATE_ALWAYS | FA_WRITE);
        
        if (result != FR_OK) {
            // ???????
            sprintf(filename, "log%lu.txt", id);
            result = f_open(&log_file, filename, FA_CREATE_ALWAYS | FA_WRITE);
            
            if (result != FR_OK) {
               //my_printf(DEBUG_USART, "?????????????: %s (????: %d)\r\n", filename, result);
                return 1;
            }
        }
    }
    
    //my_printf(DEBUG_USART, "????????????: %s\r\n", filename);
    
    // ??????
    f_close(&log_file);
    
    return 0;
}

void sys_log_set()
{
sys_log_flag=load_uint32_value_from_flash();
	
	uint32_t sys_log_flag1=sys_log_flag+1;
	save_uint32_value_to_flash(sys_log_flag1);

}



/**
 * @brief ??????????????????
 * @param log_message ??????????????????
 * @return 0-???????0-???
 */
uint8_t write_log_to_file(const char *log_message)
{
    FRESULT result;
    static FIL log_file;
    static char current_filename[64] = {0};
    char data_buffer[256];
    UINT bytes_written;
    static uint8_t file_opened = 0;
    extern uint32_t sys_log_flag;  // ??????????sys_log_flag
    
    // ?????????????????
    if (!file_opened) {
        // ???sys_log_flag????????
        sprintf(current_filename, "0:log/log%lu.txt", sys_log_flag);
        
        // ??????????????
        result = f_open(&log_file, current_filename, FA_OPEN_ALWAYS | FA_WRITE);
        if (result != FR_OK) {
            // ????????????
            sprintf(current_filename, "log/log%lu.txt", sys_log_flag);
            result = f_open(&log_file, current_filename, FA_OPEN_ALWAYS | FA_WRITE);
            
            if (result != FR_OK) {
                // ???????
                sprintf(current_filename, "log%lu.txt", sys_log_flag);
                result = f_open(&log_file, current_filename, FA_OPEN_ALWAYS | FA_WRITE);
                
                if (result != FR_OK) {
                  //  my_printf(DEBUG_USART, "???????????: %s (????: %d)\r\n", current_filename, result);
                    return 1;
                }
            }
        }
        
        // ??????????????????????????
        f_lseek(&log_file, f_size(&log_file));
        
        file_opened = 1;
       // my_printf(DEBUG_USART, "????????: %s\r\n", current_filename);
    }
    
    // ??????RTC?????????????
    rtc_parameter_struct current_time;
    rtc_current_time_get(&current_time);
    
    // ???????????? (???: 2025-01-01 00:30:19 ?????????)
    sprintf(data_buffer, "20%02x-%02x-%02x %02x:%02x:%02x %s\r\n", 
            current_time.year, current_time.month, current_time.date,
            current_time.hour, current_time.minute, current_time.second,
            log_message);
    
    // ??????
    result = f_write(&log_file, data_buffer, strlen(data_buffer), &bytes_written);
    if (result != FR_OK) {
        // ????? my_printf ??????????????
        // my_printf(DEBUG_USART, "?????????: %d\r\n", result);
        f_close(&log_file);
        file_opened = 0;
        return 2;
    }
    
    // ?????????
    f_sync(&log_file);
    
    return 0;
}


