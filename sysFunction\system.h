
#ifndef __SYSTEM_H_
#define __SYSTEM_H_
#include "gd32f470vet6.h"
#include <stdbool.h>

// ???????????



void sys_log_set(void);
void print_time(void);
void sys_all_test(void);
uint8_t tf_card_hardware_test(void);
void my_write_flash (void);
void my_reaf_flash(void);
void check_reset_source_and_init(void);
uint8_t write_log_to_file(const char *log_message);
void check_config_ini(void);
void  read_config_ini(void);
uint8_t create_log_file(uint32_t id);
float read_limit_value(void);
float read_ratio_value(void);

void save_config_values_to_flash(void);
bool load_config_values_from_flash(void);
bool load_config_values_from_flashinit(void);


bool load_config_values_from_flash(void);
bool load_limit_time_from_flash(void);


bool update_config_ratio(float ratio_value);
bool update_config_limit(float limit_value);


bool parse_and_set_datetime(const char* datetime_str);

extern float g_ratio_value ;  // ????
extern float g_limit_value; // ????
extern uint32_t sys_log_flag;

#endif /* __ADC_APP_H_ */







