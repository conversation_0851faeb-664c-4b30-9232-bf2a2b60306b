#ifndef __ADC_APP_H_
#define __ADC_APP_H_

#include "stdint.h"

// 原有函数声明
void adc_task(void);
extern uint16_t limit_time;
extern char led_bing_flag;
extern char btn_flag;
void adc_print(void);
int create_application_folders(void);
uint8_t enhanced_directory_creation_test(void);
uint8_t debug_save_voltage_to_file(float voltage);
uint8_t tf_card_error_handler(const char* operation_name, FRESULT error_code);
uint8_t tf_card_operation_with_retry(uint8_t (*operation_func)(void), uint8_t max_retries, uint16_t retry_delay_ms);
uint8_t tf_card_voltage_storage_integration_test(void);
uint8_t save_overlimit_to_file(float voltage);
uint8_t save_voltage_to_file(float voltage);
float get_scaled_dac_voltage(void);

#endif /* __ADC_APP_H_ */
