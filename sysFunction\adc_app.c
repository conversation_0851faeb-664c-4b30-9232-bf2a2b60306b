#include "gd32f470vet6.h"

extern uint16_t adc_value[1];
extern uint16_t convertarr[CONVERT_NUM];


char btn_flag=0;
uint16_t limit_time =1000;
char led_bing_flag=0;

// ??????????
static uint32_t data_count = 0;     // ??????????????????
static uint32_t max_data_per_file = 10; // ?????????????????
static uint8_t sample_dir_checked = 0;  // ???????sample??

extern FATFS fs;

void adc_print()
{
my_printf(DEBUG_USART,"Periodic Sampling\r\n");
	my_printf(DEBUG_USART,"sample cycle: %ds\r\n",limit_time/1000);
	
}


/**
 * @brief ??????????????????????(YYYYMMDDHHMMSS)
 * @param datetime_str ???????????????????????15???
 */
void get_datetime_string(char *datetime_str)
{

    uint32_t timestamp = get_system_ms();
    sprintf(datetime_str, "%014lu", timestamp);
}

/**
 * @brief 增强的电压存储函数调试版本
 * @param voltage 要保存的电压值
 * @return 0-成功，其他值-失败
 */
uint8_t debug_save_voltage_to_file(float voltage)
{
    my_printf(DEBUG_USART, "\r\n=== Debug Voltage Save Process ===\r\n");
    my_printf(DEBUG_USART, "Input voltage: %.3fV\r\n", voltage);

    FRESULT result;
    static FIL voltage_file;
    static char current_filename[64] = {0};
    char data_buffer[64];
    UINT bytes_written;
    static uint32_t debug_data_count = 0;
    static uint8_t debug_sample_dir_checked = 0;

    // 1. 检查并创建sample目录
    if (!debug_sample_dir_checked) {
        my_printf(DEBUG_USART, "1. Checking sample directory...\r\n");

        // 检查文件系统状态
        DWORD free_clusters;
        FATFS *fs_ptr;
        result = f_getfree("0:", &free_clusters, &fs_ptr);
        if (result != FR_OK) {
            my_printf(DEBUG_USART, "   ERROR: File system error: %d, attempting remount...\r\n", result);

            f_mount(0, NULL);
            delay_ms(100);
            extern FATFS fs;
            f_mount(0, &fs);
            delay_ms(100);

            result = f_getfree("0:", &free_clusters, &fs_ptr);
            if (result != FR_OK) {
                my_printf(DEBUG_USART, "   ERROR: File system still error: %d\r\n", result);
                return 1;
            }
        }
        my_printf(DEBUG_USART, "   OK: File system accessible, free clusters: %d\r\n", free_clusters);

        // 创建sample目录
        result = f_mkdir("0:sample");
        if (result != FR_OK && result != FR_EXIST) {
            my_printf(DEBUG_USART, "   Trying without drive prefix...\r\n");
            result = f_mkdir("sample");
            if (result != FR_OK && result != FR_EXIST) {
                my_printf(DEBUG_USART, "   ERROR: Failed to create sample directory: %d\r\n", result);
                return 2;
            }
        }
        my_printf(DEBUG_USART, "   OK: Sample directory ready\r\n");

        debug_sample_dir_checked = 1;
        debug_data_count = 0;
    }

    // 2. 检查是否需要创建新文件
    my_printf(DEBUG_USART, "2. Checking file creation need...\r\n");
    my_printf(DEBUG_USART, "   Current data count: %d, max per file: %d\r\n",
              debug_data_count, max_data_per_file);

    if (debug_data_count == 0 || debug_data_count >= max_data_per_file) {
        my_printf(DEBUG_USART, "   Creating new file...\r\n");

        // 关闭之前的文件
        f_close(&voltage_file);

        // 获取当前RTC时间用于文件名
        rtc_parameter_struct current_time;
        rtc_current_time_get(&current_time);

        // 生成文件名
        sprintf(current_filename, "0:sample/sampleData20%02x%02x%02x%02x%02x%02x.txt",
                current_time.year, current_time.month, current_time.date,
                current_time.hour, current_time.minute, current_time.second);

        my_printf(DEBUG_USART, "   Attempting to create: %s\r\n", current_filename);

        // 尝试创建文件
        result = f_open(&voltage_file, current_filename, FA_CREATE_ALWAYS | FA_WRITE);
        if (result != FR_OK) {
            my_printf(DEBUG_USART, "   Failed, trying without drive prefix...\r\n");
            sprintf(current_filename, "sample/sampleData20%02x%02x%02x%02x%02x%02x.txt",
                    current_time.year, current_time.month, current_time.date,
                    current_time.hour, current_time.minute, current_time.second);
            result = f_open(&voltage_file, current_filename, FA_CREATE_ALWAYS | FA_WRITE);

            if (result != FR_OK) {
                my_printf(DEBUG_USART, "   Failed, trying root directory...\r\n");
                sprintf(current_filename, "sampleData20%02x%02x%02x%02x%02x%02x.txt",
                        current_time.year, current_time.month, current_time.date,
                        current_time.hour, current_time.minute, current_time.second);
                result = f_open(&voltage_file, current_filename, FA_CREATE_ALWAYS | FA_WRITE);

                if (result != FR_OK) {
                    my_printf(DEBUG_USART, "   ERROR: Failed to create file: %s (Error: %d)\r\n",
                              current_filename, result);
                    return 3;
                }
            }
        }

        my_printf(DEBUG_USART, "   OK: File created: %s\r\n", current_filename);
        debug_data_count = 0;
    }

    // 3. 准备数据
    my_printf(DEBUG_USART, "3. Preparing data...\r\n");
    rtc_parameter_struct current_time;
    rtc_current_time_get(&current_time);

    sprintf(data_buffer, "20%02x-%02x-%02x %02x:%02x:%02x %.1fV\r\n",
            current_time.year, current_time.month, current_time.date,
            current_time.hour, current_time.minute, current_time.second,
            voltage);

    my_printf(DEBUG_USART, "   Data to write: %s", data_buffer);
    my_printf(DEBUG_USART, "   Data length: %d bytes\r\n", strlen(data_buffer));

    // 4. 写入数据
    my_printf(DEBUG_USART, "4. Writing data to file...\r\n");
    result = f_write(&voltage_file, data_buffer, strlen(data_buffer), &bytes_written);
    if (result != FR_OK) {
        my_printf(DEBUG_USART, "   ERROR: Write failed, error: %d\r\n", result);
        f_close(&voltage_file);
        return 4;
    }

    if (bytes_written != strlen(data_buffer)) {
        my_printf(DEBUG_USART, "   ERROR: Incomplete write, expected: %d, actual: %d\r\n",
                  strlen(data_buffer), bytes_written);
        f_close(&voltage_file);
        return 5;
    }

    my_printf(DEBUG_USART, "   OK: %d bytes written successfully\r\n", bytes_written);

    // 5. 同步数据到存储设备
    my_printf(DEBUG_USART, "5. Syncing data to storage...\r\n");
    result = f_sync(&voltage_file);
    if (result != FR_OK) {
        my_printf(DEBUG_USART, "   ERROR: Sync failed, error: %d\r\n", result);
        return 6;
    }
    my_printf(DEBUG_USART, "   OK: Data synced\r\n");

    // 6. 更新计数器
    debug_data_count++;
    my_printf(DEBUG_USART, "6. Updated data count: %d\r\n", debug_data_count);

    my_printf(DEBUG_USART, "=== Debug Voltage Save COMPLETED ===\r\n\r\n");
    return 0;
}

/**
 * @brief 原有的电压存储函数（保持兼容性）
 * @param voltage 要保存的电压值
 * @return 0-成功，其他值-失败
 */
uint8_t save_voltage_to_file(float voltage)
{
    FRESULT result;
    static FIL voltage_file;
    static char current_filename[64] = {0};
    char data_buffer[64];
    UINT bytes_written;
    
    // ??????????????????sample??
    if (!sample_dir_checked) {
        // ??????????
        DWORD free_clusters;
        FATFS *fs_ptr;
        
        // ??????FATFS??????
        result = f_getfree("0:", &free_clusters, &fs_ptr);
        if (result != FR_OK) {
            my_printf(DEBUG_USART, "File system error: %d, trying to remount...\r\n", result);
            f_mount(0, NULL);
            delay_ms(100);
            extern FATFS fs; // ???????????????????
            f_mount(0, &fs); // ???????
            delay_ms(100);
            
            // ??????
            result = f_getfree("0:", &free_clusters, &fs_ptr);
            if (result != FR_OK) {
                my_printf(DEBUG_USART, "File system still error: %d\r\n", result);
                return 1;
            }
        }
        
        // ????sample?? - ??????????????????
        result = f_mkdir("0:sample");
        if (result != FR_OK && result != FR_EXIST) {
            // ???????????????
            result = f_mkdir("sample");
            if (result != FR_OK && result != FR_EXIST) {
                my_printf(DEBUG_USART, "Failed to create sample directory: %d\r\n", result);
                return 2;
            }
        }
        
        sample_dir_checked = 1;
        data_count = 0;
    }
    
    // ??????????????????????????
    if (data_count == 0 || data_count >= max_data_per_file) {
        // ??????????
        f_close(&voltage_file);
        
        // ??????RTC????????????
        rtc_parameter_struct current_time;
        rtc_current_time_get(&current_time);
        
        // ?????????????????? (???: sampleData20250101003010.txt)
        sprintf(current_filename, "0:sample/sampleData20%02x%02x%02x%02x%02x%02x.txt", 
                current_time.year, current_time.month, current_time.date,
                current_time.hour, current_time.minute, current_time.second);
        
        // ?????????
        result = f_open(&voltage_file, current_filename, FA_CREATE_ALWAYS | FA_WRITE);
        if (result != FR_OK) {
            // ??????????????
            sprintf(current_filename, "sample/sampleData20%02x%02x%02x%02x%02x%02x.txt", 
                    current_time.year, current_time.month, current_time.date,
                    current_time.hour, current_time.minute, current_time.second);
            result = f_open(&voltage_file, current_filename, FA_CREATE_ALWAYS | FA_WRITE);
            
            if (result != FR_OK) {
                // ???????
                sprintf(current_filename, "sampleData20%02x%02x%02x%02x%02x%02x.txt", 
                        current_time.year, current_time.month, current_time.date,
                        current_time.hour, current_time.minute, current_time.second);
                result = f_open(&voltage_file, current_filename, FA_CREATE_ALWAYS | FA_WRITE);
                
                if (result != FR_OK) {
                    my_printf(DEBUG_USART, "Failed to create voltage file: %s (Error: %d)\r\n", current_filename, result);
                    return 3;
                }
            }
        }
        
       // my_printf(DEBUG_USART, "Created new voltage file: %s\r\n", current_filename);
        data_count = 0;
    }
    
    // ??????RTC?????????????
    rtc_parameter_struct current_time;
    rtc_current_time_get(&current_time);
    
    // ???????????? (???: 2025-01-01 00:30:19 1.5V)
    sprintf(data_buffer, "20%02x-%02x-%02x %02x:%02x:%02x %.1fV\r\n", 
            current_time.year, current_time.month, current_time.date,
            current_time.hour, current_time.minute, current_time.second,
            voltage);
    
    // ???????
    result = f_write(&voltage_file, data_buffer, strlen(data_buffer), &bytes_written);
    if (result != FR_OK) {
        my_printf(DEBUG_USART, "Voltage write error: %d\r\n", result);
        f_close(&voltage_file);
        return 4;
    }
    
    // ???????????
    f_sync(&voltage_file);
    
    // ???????????
    data_count++;
    
    return 0;
}



/**
 * @brief ????????????????????????????hideData????????
 * @param voltage ?????????
 * @param unix_timestamp ????
 * @param voltage_int_part ???????????
 * @param voltage_frac_part ???????????
 * @return 0-???????0-???
 */
uint8_t save_hide_data_to_file(float voltage, uint32_t unix_timestamp, uint16_t voltage_int_part, uint16_t voltage_frac_part)
{
    FRESULT result;
    static FIL hide_file;
    static char current_filename[64] = {0};
    char data_buffer[128];
    UINT bytes_written;
    static uint32_t hide_data_count = 0;     // ??????????????????
    static uint8_t hide_dir_checked = 0;     // ???????hideData??
    
    // ??????????????????hideData??
    if (!hide_dir_checked) {
        // ??????????
        DWORD free_clusters;
        FATFS *fs_ptr;
        
        // ??????FATFS??????
        result = f_getfree("0:", &free_clusters, &fs_ptr);
        if (result != FR_OK) {
            my_printf(DEBUG_USART, "?????????: %d, ???????????...\r\n", result);
            f_mount(0, NULL);
            delay_ms(100);
            extern FATFS fs; // ???????????????????
            f_mount(0, &fs); // ???????
            delay_ms(100);
            
            // ??????
            result = f_getfree("0:", &free_clusters, &fs_ptr);
            if (result != FR_OK) {
                my_printf(DEBUG_USART, "????????????: %d\r\n", result);
                return 1;
            }
        }
        
        // ????hideData??
        result = f_mkdir("0:hideData");
        if (result != FR_OK && result != FR_EXIST) {
            // ???????????????
            result = f_mkdir("hideData");
            if (result != FR_OK && result != FR_EXIST) {
                my_printf(DEBUG_USART, "????hideData?????: %d\r\n", result);
                return 2;
            }
        }
        
        hide_dir_checked = 1;
        hide_data_count = 0;
    }
    
    // ??????????????????????????
    if (hide_data_count == 0 || hide_data_count >= 10) {
        // ??????????
        f_close(&hide_file);
        
        // ??????RTC???????????
        rtc_parameter_struct current_time;
        rtc_current_time_get(&current_time);
        
        // ????????????????? (???: hideData20250101003010.txt)
        sprintf(current_filename, "0:hideData/hideData20%02x%02x%02x%02x%02x%02x.txt", 
                current_time.year, current_time.month, current_time.date,
                current_time.hour, current_time.minute, current_time.second);
        
        // ?????????
        result = f_open(&hide_file, current_filename, FA_CREATE_ALWAYS | FA_WRITE);
        if (result != FR_OK) {
            // ?????????????
            sprintf(current_filename, "hideData/hideData20%02x%02x%02x%02x%02x%02x.txt", 
                    current_time.year, current_time.month, current_time.date,
                    current_time.hour, current_time.minute, current_time.second);
            result = f_open(&hide_file, current_filename, FA_CREATE_ALWAYS | FA_WRITE);
            
            if (result != FR_OK) {
                // ???????
                sprintf(current_filename, "hideData20%02x%02x%02x%02x%02x%02x.txt", 
                        current_time.year, current_time.month, current_time.date,
                        current_time.hour, current_time.minute, current_time.second);
                result = f_open(&hide_file, current_filename, FA_CREATE_ALWAYS | FA_WRITE);
                
                if (result != FR_OK) {
                    my_printf(DEBUG_USART, "??????????????????: %s (????: %d)\r\n", current_filename, result);
                    return 3;
                }
            }
        }
        
        //my_printf(DEBUG_USART, "??????????????????: %s\r\n", current_filename);
        hide_data_count = 0;
    }
    
    // ??????RTC?????????????
    rtc_parameter_struct current_time;
    rtc_current_time_get(&current_time);
    
    // ???????????? (???: 2025-01-01 00:30:19 1.5V ?????? hide:XXXXXXXXXXXXXXXX)
    sprintf(data_buffer, "20%02x-%02x-%02x %02x:%02x:%02x %.1fV\r\nhide:%08X%04X%04X\r\n", 
            current_time.year, current_time.month, current_time.date,
            current_time.hour, current_time.minute, current_time.second,
            voltage, unix_timestamp, voltage_int_part, voltage_frac_part);
    
    // ???????
    result = f_write(&hide_file, data_buffer, strlen(data_buffer), &bytes_written);
    if (result != FR_OK) {
        my_printf(DEBUG_USART, "???????????????: %d\r\n", result);
        f_close(&hide_file);
        return 4;
    }
    
    // ???????????
    f_sync(&hide_file);
    
    // ???????????
    hide_data_count++;
    
    return 0;
}







uint32_t convert_rtc_to_unix_timestamp(rtc_parameter_struct* time)
{
    // ??BCD???????????
    uint16_t year = ((time->year >> 4) & 0x0F) * 10 + (time->year & 0x0F) + 2000;
    uint8_t month = ((time->month >> 4) & 0x0F) * 10 + (time->month & 0x0F);
    uint8_t day = ((time->date >> 4) & 0x0F) * 10 + (time->date & 0x0F);
    uint8_t hour = ((time->hour >> 4) & 0x0F) * 10 + (time->hour & 0x0F);
    uint8_t minute = ((time->minute >> 4) & 0x0F) * 10 + (time->minute & 0x0F);
    uint8_t second = ((time->second >> 4) & 0x0F) * 10 + (time->second & 0x0F);
    
    // ???????????
    if (month < 1 || month > 12 || day < 1 || day > 31 || hour > 23 || minute > 59 || second > 59) {
        return 0; // ?????????????0
    }

    // ?????1970???????????
    uint32_t days = 0;

    // ?????1970??????????????????
    for (uint16_t y = 1970; y < year; y++) {
        days += 365 + (y % 4 == 0 && (y % 100 != 0 || y % 400 == 0) ? 1 : 0);
    }

    // ?????????1??????????????????????
    uint8_t days_in_month[] = {0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};

    // ????????2??
    if (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0)) {
        days_in_month[2] = 29;
    }

    // ????????????
    for (uint8_t m = 1; m < month; m++) {
        days += days_in_month[m];
    }

    // ????????????
    days += day - 1;

    // ??????????
    uint32_t timestamp = days * 86400;
    timestamp += hour * 3600;
    timestamp += minute * 60;
    timestamp += second;

    // ????????????????+8?????
    timestamp -= 28800;  // 8???????8 * 3600??

    return timestamp;
}



/**
 * @brief ??????DAC???????????????????????
 * @return ????DAC??????????????????(????:V)
 * @note DAC????????3.3V??12??DAC(0-4095)???0-3.3V
 */
float get_scaled_dac_voltage(void)
{
    uint16_t dac_value;
    float dac_voltage;
    float scaled_voltage;
    
    // ???DAC0 OUT0???????????(0-4095)
    dac_value = adc_value[0];
    
    // ??DAC?(0-4095)???????(0-3.3V)
    dac_voltage = (float)dac_value * 3.3f / 4095.0f;
    
    // ????????????????
    scaled_voltage = dac_voltage * g_ratio_value;
    
    // ??????????
//    my_printf(DEBUG_USART, "DAC???: %u, ??????: %.3fV, ???????: %.2f, ???????: %.3fV\r\n", 
//              dac_value, dac_voltage, g_ratio_value, scaled_voltage);
    
    return scaled_voltage;
}




/**
 * @brief ????????????over_limit????????
 * @param voltage ?????????
 * @return 0-???????0-???
 */
uint8_t save_overlimit_to_file(float voltage)
{
    FRESULT result;
    static FIL overlimit_file;
    static char current_filename[64] = {0};
    char data_buffer[64];
    UINT bytes_written;
    static uint32_t overlimit_count = 0;     // ??????????????????
    static uint8_t overlimit_dir_checked = 0;  // ???????over_limit??
    
    // ??????????????????over_limit??
    if (!overlimit_dir_checked) {
        // ??????????
        DWORD free_clusters;
        FATFS *fs_ptr;
        
        // ??????FATFS??????
        result = f_getfree("0:", &free_clusters, &fs_ptr);
        if (result != FR_OK) {
            my_printf(DEBUG_USART, "File system error: %d, trying to remount...\r\n", result);
            f_mount(0, NULL);
            delay_ms(100);
            extern FATFS fs; // ???????????????????
            f_mount(0, &fs); // ???????
            delay_ms(100);
            
            // ??????
            result = f_getfree("0:", &free_clusters, &fs_ptr);
            if (result != FR_OK) {
                my_printf(DEBUG_USART, "File system still error: %d\r\n", result);
                return 1;
            }
        }
        
        // ????over_limit??
        result = f_mkdir("0:over_limit");
        if (result != FR_OK && result != FR_EXIST) {
            // ???????????????
            result = f_mkdir("over_limit");
            if (result != FR_OK && result != FR_EXIST) {
                my_printf(DEBUG_USART, "Failed to create over_limit directory: %d\r\n", result);
                return 2;
            }
        }
        
        overlimit_dir_checked = 1;
        overlimit_count = 0;
    }
    
    // ??????????????????????????
    if (overlimit_count == 0 || overlimit_count >= 10) {
        // ??????????
        f_close(&overlimit_file);
        
        // ??????RTC???????????
        rtc_parameter_struct current_time;
        rtc_current_time_get(&current_time);
        
        // ????????????????? (???: overLimit20250101003010.txt)
        sprintf(current_filename, "0:over_limit/overLimit20%02x%02x%02x%02x%02x%02x.txt", 
                current_time.year, current_time.month, current_time.date,
                current_time.hour, current_time.minute, current_time.second);
        
        // ?????????
        result = f_open(&overlimit_file, current_filename, FA_CREATE_ALWAYS | FA_WRITE);
        if (result != FR_OK) {
            // ?????????????
            sprintf(current_filename, "over_limit/overLimit20%02x%02x%02x%02x%02x%02x.txt", 
                    current_time.year, current_time.month, current_time.date,
                    current_time.hour, current_time.minute, current_time.second);
            result = f_open(&overlimit_file, current_filename, FA_CREATE_ALWAYS | FA_WRITE);
            
            if (result != FR_OK) {
                // ???????
                sprintf(current_filename, "overLimit20%02x%02x%02x%02x%02x%02x.txt", 
                        current_time.year, current_time.month, current_time.date,
                        current_time.hour, current_time.minute, current_time.second);
                result = f_open(&overlimit_file, current_filename, FA_CREATE_ALWAYS | FA_WRITE);
                
                if (result != FR_OK) {
                    my_printf(DEBUG_USART, "Failed to create overlimit file: %s (Error: %d)\r\n", current_filename, result);
                    return 3;
                }
            }
        }
        
        //my_printf(DEBUG_USART, "Created new overlimit file: %s\r\n", current_filename);
        overlimit_count = 0;
    }
    
    // ??????RTC?????????????
    rtc_parameter_struct current_time;
    rtc_current_time_get(&current_time);
    
    // ???????????? (????: 2025-01-01 00:30:19 30V limit 10V)
    // ????????? "limit" ?????????
    sprintf(data_buffer, "20%02x-%02x-%02x %02x:%02x:%02x %.1fV limit %.1fV\r\n", 
            current_time.year, current_time.month, current_time.date,
            current_time.hour, current_time.minute, current_time.second,
            voltage, g_limit_value);
    
    // ?????????????????
    //my_printf(DEBUG_USART, "Saving to file: %s", data_buffer);
    
    // ???????
    result = f_write(&overlimit_file, data_buffer, strlen(data_buffer), &bytes_written);
    if (result != FR_OK) {
        my_printf(DEBUG_USART, "Overlimit write error: %d\r\n", result);
        f_close(&overlimit_file);
        return 4;
    }
    
    // ???????????
    f_sync(&overlimit_file);
    
    // ???????????
    overlimit_count++;
    
    return 0;
}







void hide_tv()
{
            // ??????RTC???
            rtc_parameter_struct current_time;
            rtc_current_time_get(&current_time);
            
            // ??????????
            float voltage = get_scaled_dac_voltage();
            
            // ??BCD?????RTC???????Unix????
            uint32_t unix_timestamp = convert_rtc_to_unix_timestamp(&current_time);
            
            // ????????????????
            uint16_t voltage_int_part = (uint16_t)voltage;                    // ????????
            uint16_t voltage_frac_part = (uint16_t)((voltage - voltage_int_part) * 65536.0f); // ????????
            
            // ?????????????
            //my_printf(DEBUG_USART, "????: %08X\r\n", unix_timestamp);
            //my_printf(DEBUG_USART, "????: %04X%04X\r\n", voltage_int_part, voltage_frac_part);
	         if(voltage > g_limit_value) {
                my_printf(DEBUG_USART, "%08X%04X%04X*\r\n", unix_timestamp, voltage_int_part, voltage_frac_part);
						 save_hide_data_to_file(voltage, unix_timestamp, voltage_int_part, voltage_frac_part);

            } else {
                my_printf(DEBUG_USART, "%08X%04X%04X\r\n", unix_timestamp, voltage_int_part, voltage_frac_part);
							save_hide_data_to_file(voltage, unix_timestamp, voltage_int_part, voltage_frac_part);
            }
           // my_printf(DEBUG_USART, "%08X%04X%04X\r\n", unix_timestamp, voltage_int_part, voltage_frac_part);
            
           // usart_flag = 25;
}


/**
 * @brief ??????????DAC??????
 * @note ???: YYYY-MM-DD HH:MM:SS ch0=XX.XV
 */
void print_time_and_voltage(void)
{
    rtc_parameter_struct current_time;
    float voltage;
    
    // ??????RTC???
    rtc_current_time_get(&current_time);
    
    // ??????DAC??????????????
    voltage = get_scaled_dac_voltage();
    
    // ?????????
	if(voltage>g_limit_value)
	{
//	    my_printf(DEBUG_USART, "20%02x-%02x-%02x %02x:%02x:%02x ch0=%.1fV\r\n",
//              current_time.year, current_time.month, current_time.date,
//              current_time.hour, current_time.minute, current_time.second,
//              voltage);
        my_printf(DEBUG_USART, "20%02x-%02x-%02x %02x:%02x:%02x ch0=%.1fV OverLimit (%.2f) !\r\n",
              current_time.year, current_time.month, current_time.date,
              current_time.hour, current_time.minute, current_time.second,
              voltage, g_limit_value);
				save_overlimit_to_file(get_scaled_dac_voltage());
	}
	else
	{
	        my_printf(DEBUG_USART, "20%02x-%02x-%02x %02x:%02x:%02x ch0=%.1fV\r\n",
              current_time.year, current_time.month, current_time.date,
              current_time.hour, current_time.minute, current_time.second,
              voltage);
					save_voltage_to_file(get_scaled_dac_voltage());


	}
    // ??OLED?????????????? hh:mm:ss??
    oled_printf(0, 0, "%02x:%02x:%02x      ", 
                current_time.hour, current_time.minute, current_time.second);
    
    // ??OLED????????????????? xx.xx V??
    oled_printf(0, 1, "%.2f V              ", voltage);

}

              






/**
 * @brief 增强的目录创建和验证函数
 * @return 0-成功，其他值-失败
 */
uint8_t enhanced_directory_creation_test(void)
{
    my_printf(DEBUG_USART, "\r\n=== Directory Creation Test ===\r\n");

    FRESULT res;
    DIR dir;
    FILINFO fno;

    // 目录列表和描述
    const char *folder_names[] = {
        "sample",     // 电压采样数据目录
        "over_limit", // 超限数据目录
        "log",        // 系统日志目录
        "hideData"    // 隐藏数据目录
    };

    const char *folder_descriptions[] = {
        "voltage sampling data",
        "over-limit data",
        "system logs",
        "hidden data"
    };

    int folder_count = sizeof(folder_names) / sizeof(folder_names[0]);
    int success_count = 0;

    // 1. 检查文件系统是否可写
    my_printf(DEBUG_USART, "1. Checking file system write capability...\r\n");
    DWORD free_clusters;
    FATFS *fs_ptr;
    res = f_getfree("0:", &free_clusters, &fs_ptr);
    if (res != FR_OK) {
        my_printf(DEBUG_USART, "   ERROR: Cannot access file system, error: %d\r\n", res);
        return 1;
    }

    if (free_clusters == 0) {
        my_printf(DEBUG_USART, "   ERROR: No free space available\r\n");
        return 2;
    }
    my_printf(DEBUG_USART, "   OK: File system accessible, free clusters: %d\r\n", free_clusters);

    // 2. 创建目录
    my_printf(DEBUG_USART, "2. Creating application directories...\r\n");
    for (int i = 0; i < folder_count; i++) {
        my_printf(DEBUG_USART, "   Creating '%s' (%s)...\r\n",
                  folder_names[i], folder_descriptions[i]);

        res = f_mkdir(folder_names[i]);

        if (res == FR_OK) {
            my_printf(DEBUG_USART, "      OK: Directory created\r\n");
            success_count++;
        }
        else if (res == FR_EXIST) {
            my_printf(DEBUG_USART, "      OK: Directory already exists\r\n");
            success_count++;
        }
        else {
            my_printf(DEBUG_USART, "      ERROR: Failed to create directory, error: %d\r\n", res);

            // 尝试使用8.3格式的短名称 (FAT文件系统兼容)
            char short_name[9];
            strncpy(short_name, folder_names[i], 8);
            short_name[8] = '\0';

            my_printf(DEBUG_USART, "      Trying short name: '%s'\r\n", short_name);
            res = f_mkdir(short_name);

            if (res == FR_OK || res == FR_EXIST) {
                my_printf(DEBUG_USART, "      OK: Short name directory created/exists\r\n");
                success_count++;
            } else {
                my_printf(DEBUG_USART, "      ERROR: Short name also failed, error: %d\r\n", res);
            }
        }
    }

    // 3. 验证目录是否可访问
    my_printf(DEBUG_USART, "3. Verifying directory accessibility...\r\n");
    int verified_count = 0;

    for (int i = 0; i < folder_count; i++) {
        res = f_opendir(&dir, folder_names[i]);
        if (res == FR_OK) {
            f_closedir(&dir);
            my_printf(DEBUG_USART, "   OK: '%s' is accessible\r\n", folder_names[i]);
            verified_count++;
        } else {
            my_printf(DEBUG_USART, "   ERROR: Cannot access '%s', error: %d\r\n",
                      folder_names[i], res);
        }
    }

    // 4. 测试目录写入权限
    my_printf(DEBUG_USART, "4. Testing directory write permissions...\r\n");
    FIL test_file;
    char test_path[32];
    int write_test_count = 0;

    for (int i = 0; i < folder_count; i++) {
        sprintf(test_path, "%s/test.tmp", folder_names[i]);
        res = f_open(&test_file, test_path, FA_CREATE_ALWAYS | FA_WRITE);

        if (res == FR_OK) {
            const char *test_data = "test";
            UINT bytes_written;
            f_write(&test_file, test_data, 4, &bytes_written);
            f_close(&test_file);
            f_unlink(test_path);  // 删除测试文件

            my_printf(DEBUG_USART, "   OK: '%s' is writable\r\n", folder_names[i]);
            write_test_count++;
        } else {
            my_printf(DEBUG_USART, "   ERROR: Cannot write to '%s', error: %d\r\n",
                      folder_names[i], res);
        }
    }

    // 5. 结果汇总
    my_printf(DEBUG_USART, "5. Test Results Summary:\r\n");
    my_printf(DEBUG_USART, "   Directories created/verified: %d/%d\r\n", success_count, folder_count);
    my_printf(DEBUG_USART, "   Directories accessible: %d/%d\r\n", verified_count, folder_count);
    my_printf(DEBUG_USART, "   Directories writable: %d/%d\r\n", write_test_count, folder_count);

    if (success_count == folder_count && verified_count == folder_count && write_test_count == folder_count) {
        my_printf(DEBUG_USART, "=== Directory Creation Test PASSED ===\r\n\r\n");
        return 0;
    } else {
        my_printf(DEBUG_USART, "=== Directory Creation Test FAILED ===\r\n\r\n");
        return 3;
    }
}

/**
 * @brief 原有的目录创建函数（保持兼容性）
 * @return 0:成功 -1:失败
 */
int create_application_folders(void)
{
    FRESULT res;
    int success_count = 0;

    // ?????????????????????? - ?????overLimit?over_limit?????????????
    const char *folder_names[] = {
        "sample",     // ?????????????
        "over_limit", // ????????????? (???????????)
        "log",        // ????????
        "hideData"    // ?????????????
    };

    // ????????????
    for (int i = 0; i < 4; i++) {
        res = f_mkdir(folder_names[i]);

        if (res == FR_OK) {
           // my_printf(DEBUG_USART, "????????????: %s\r\n", folder_names[i]);
            success_count++;
        }
        else if (res == FR_EXIST) {
            //my_printf(DEBUG_USART, "??????????: %s\r\n", folder_names[i]);
            success_count++;
        }
        else {
          //  my_printf(DEBUG_USART, "???????????? %s, ??????: %d\r\n", folder_names[i], res);

            // ???????8.3?????????? (FAT??????)
            char short_name[9]; // 8???+??????
            strncpy(short_name, folder_names[i], 8);
            short_name[8] = '\0';

            res = f_mkdir(short_name);
            if (res == FR_OK || res == FR_EXIST) {
                my_printf(DEBUG_USART, "?????????????????????: %s\r\n", short_name);
                success_count++;
            } else {
               // my_printf(DEBUG_USART, "???????????????: %s, ??????: %d\r\n", short_name, res);
            }
        }
    }

    // ????????????????????????
    if (success_count == 4) {
        //my_printf(DEBUG_USART, "????????????????\r\n");
        return 0;
    } else {
       // my_printf(DEBUG_USART, "????????????????????SD??\r\n");
        return -1;
    }
}










void sd_system()
{
		if(usart_flag==6)
		{
		  check_config_ini();
			 read_config_ini();
				
		usart_flag=7;
		}
		if(usart_flag==10)
		{
			if(ratio_value>=0&&ratio_value<=100)
			{
					update_config_ratio(ratio_value);
				
					usart_flag=11;		
			}
			else{
					my_printf(DEBUG_USART,"ratio invalid\r\n");
					my_printf(DEBUG_USART,"ratio=%1.f\r\n",g_ratio_value);
				usart_flag=7;
			}

		}
		if(usart_flag==14)
		{
			if(limit_value>=0&&limit_value<=200)
			{
					update_config_limit(limit_value);
					usart_flag=15;


			}
			else{
					my_printf(DEBUG_USART,"limit invalid\r\n");
					my_printf(DEBUG_USART,"limit=%1.f\r\n",g_limit_value);
				usart_flag=7;
			}
		}
		
		if(usart_flag==16)
		{
		save_config_values_to_flash();
		
		usart_flag=17;
		}
		
		if(usart_flag==18)
		{
		
		load_config_values_from_flash();

				usart_flag=19;
		}
		if(usart_flag==20)
		{
			adc_print();
			led_bing_flag=1;
			usart_flag=21;
		}
		if(usart_flag==21)
		{
				if(det_adc_time>limit_time)
					{
						det_adc_time=0;
						if(hide_flag==0)
						{
						print_time_and_voltage();
						
						//write_log_to_file("ratio");

			
							
						}
						else if(hide_flag==1)
						{
   					 		oled_printf(0,0,"system idle     ");
								oled_printf(0,1,"                ");	
								hide_tv();
						}
					}
		}
		if(usart_flag==22)
		{
		led_bing_flag=0;	
		oled_printf(0,0,"system idle     ");
		oled_printf(0,1,"                ");	
		ucLed[0]=0;
			usart_flag=23;
		}
		if(usart_flag==24)
		{
		
		 
			
		
		}
		
		
		
}





bool btn_data()
{
if(btn_flag==0)
	return false;
btn_flag=0;
uint8_t write_buffer[SPI_FLASH_PAGE_SIZE];
    uint8_t read_buffer[SPI_FLASH_PAGE_SIZE];
    uint32_t target_addr = 0x002000; // ???????????????????
    
    //my_printf(DEBUG_USART, "=== ???????????????Flash ===\r\n");
    
    // ???????
    spi_flash_sector_erase(target_addr);
    
    // ???????????
    memset(write_buffer, 0x00, SPI_FLASH_PAGE_SIZE);
    
    // ??limit_time???????????
    memcpy(write_buffer, &limit_time, sizeof(uint16_t));
    
    // ????????
    spi_flash_buffer_write(write_buffer, target_addr, SPI_FLASH_PAGE_SIZE);
    
    // ?????????????
    memset(read_buffer, 0x00, SPI_FLASH_PAGE_SIZE);
    spi_flash_buffer_read(read_buffer, target_addr, SPI_FLASH_PAGE_SIZE);
    
    // ?????????????????
    uint16_t read_limit_time;
    memcpy(&read_limit_time, read_buffer, sizeof(uint16_t));
    
    //my_printf(DEBUG_USART, "????????????: %dms\r\n", read_limit_time);
    my_printf(DEBUG_USART,"sample cycle adhust: %ds\r\n",read_limit_time/1000);
    // ???
    if (memcmp(write_buffer, read_buffer, sizeof(uint16_t)) == 0)
    {
       // my_printf(DEBUG_USART, "???????????????Flash??\r\n");
        return true;
    }
    else
    {
        my_printf(DEBUG_USART, "???????????????\r\n");
        return false;
    }
}

/**
 * @brief ???????? - ??????????TF?????
 * @return 0-??????-??
 */
uint8_t tf_card_voltage_storage_test(void)
{
    my_printf(DEBUG_USART, "\r\n=== TF Card Voltage Storage Integration Test ===\r\n");

    // 1. ??TF?????
    my_printf(DEBUG_USART, "1. Checking TF card hardware status...\r\n");
    sd_error_enum sd_status = sd_init();
    if (sd_status != SD_OK) {
        my_printf(DEBUG_USART, "   ERROR: TF card initialization failed, status: %d\r\n", sd_status);
        return 1;
    }

    uint32_t capacity = sd_card_capacity_get();
    my_printf(DEBUG_USART, "   OK: TF card detected, capacity: %d KB\r\n", capacity);

    // 2. ??????????
    my_printf(DEBUG_USART, "2. Checking file system mount status...\r\n");
    DWORD free_clusters;
    FATFS *fs_ptr;
    FRESULT fs_result = f_getfree("0:", &free_clusters, &fs_ptr);
    if (fs_result != FR_OK) {
        my_printf(DEBUG_USART, "   ERROR: File system not mounted properly, error: %d\r\n", fs_result);
        my_printf(DEBUG_USART, "   Attempting to remount...\r\n");

        extern FATFS fs;
        f_mount(0, NULL);
        delay_ms(100);
        f_mount(0, &fs);
        delay_ms(100);

        fs_result = f_getfree("0:", &free_clusters, &fs_ptr);
        if (fs_result != FR_OK) {
            my_printf(DEBUG_USART, "   ERROR: Remount failed, error: %d\r\n", fs_result);
            return 2;
        }
    }
    my_printf(DEBUG_USART, "   OK: File system mounted, free clusters: %d\r\n", free_clusters);

    // 3. ??????
    my_printf(DEBUG_USART, "3. Checking directory structure...\r\n");
    int folder_result = create_application_folders();
    if (folder_result != 0) {
        my_printf(DEBUG_USART, "   ERROR: Failed to create application folders\r\n");
        return 3;
    }
    my_printf(DEBUG_USART, "   OK: All required directories created/verified\r\n");

    // 4. ??????
    my_printf(DEBUG_USART, "4. Testing voltage acquisition...\r\n");
    float test_voltage = get_scaled_dac_voltage();
    my_printf(DEBUG_USART, "   Current voltage reading: %.3fV\r\n", test_voltage);
    my_printf(DEBUG_USART, "   Ratio: %.2f, Limit: %.2fV\r\n", g_ratio_value, g_limit_value);

    // 5. ????????
    my_printf(DEBUG_USART, "5. Testing voltage data storage...\r\n");
    uint8_t save_result = save_voltage_to_file(test_voltage);
    if (save_result != 0) {
        my_printf(DEBUG_USART, "   ERROR: Failed to save voltage data, error code: %d\r\n", save_result);
        return 4;
    }
    my_printf(DEBUG_USART, "   OK: Voltage data saved successfully\r\n");

    // 6. ????????????????
    if (test_voltage > g_limit_value) {
        my_printf(DEBUG_USART, "6. Testing over-limit data storage...\r\n");
        uint8_t overlimit_result = save_overlimit_to_file(test_voltage);
        if (overlimit_result != 0) {
            my_printf(DEBUG_USART, "   ERROR: Failed to save over-limit data, error code: %d\r\n", overlimit_result);
            return 5;
        }
        my_printf(DEBUG_USART, "   OK: Over-limit data saved successfully\r\n");
    } else {
        my_printf(DEBUG_USART, "6. Voltage within limit, skipping over-limit test\r\n");
    }

    // 7. ??????
    my_printf(DEBUG_USART, "7. Testing log file writing...\r\n");
    uint8_t log_result = write_log_to_file("TF card integration test completed");
    if (log_result != 0) {
        my_printf(DEBUG_USART, "   ERROR: Failed to write log, error code: %d\r\n", log_result);
        return 6;
    }
    my_printf(DEBUG_USART, "   OK: Log written successfully\r\n");

    my_printf(DEBUG_USART, "=== Integration Test PASSED ===\r\n\r\n");
    return 0;
}

/**
 * @brief TF卡操作错误处理和重试机制
 * @param operation_name 操作名称
 * @param error_code FATFS错误代码
 * @return 0-可以重试，1-严重错误不可重试
 */
uint8_t tf_card_error_handler(const char* operation_name, FRESULT error_code)
{
    my_printf(DEBUG_USART, "\r\n=== TF Card Error Handler ===\r\n");
    my_printf(DEBUG_USART, "Operation: %s\r\n", operation_name);
    my_printf(DEBUG_USART, "Error Code: %d\r\n", error_code);

    switch (error_code) {
        case FR_OK:
            my_printf(DEBUG_USART, "Status: Success\r\n");
            return 0;

        case FR_DISK_ERR:
            my_printf(DEBUG_USART, "Error: Disk I/O error - Hardware problem\r\n");
            my_printf(DEBUG_USART, "Recommendation: Check TF card connection\r\n");
            return 1; // 严重错误

        case FR_INT_ERR:
            my_printf(DEBUG_USART, "Error: Internal error - Software problem\r\n");
            my_printf(DEBUG_USART, "Recommendation: System restart may be needed\r\n");
            return 1; // 严重错误

        case FR_NOT_READY:
            my_printf(DEBUG_USART, "Error: Drive not ready - Initialization issue\r\n");
            my_printf(DEBUG_USART, "Recommendation: Retry initialization\r\n");
            return 0; // 可以重试

        case FR_NO_FILE:
            my_printf(DEBUG_USART, "Error: File not found\r\n");
            my_printf(DEBUG_USART, "Recommendation: Check file path\r\n");
            return 0; // 可以重试

        case FR_NO_PATH:
            my_printf(DEBUG_USART, "Error: Path not found\r\n");
            my_printf(DEBUG_USART, "Recommendation: Create directory first\r\n");
            return 0; // 可以重试

        case FR_INVALID_NAME:
            my_printf(DEBUG_USART, "Error: Invalid file/directory name\r\n");
            my_printf(DEBUG_USART, "Recommendation: Use valid FAT32 naming\r\n");
            return 1; // 严重错误

        case FR_DENIED:
            my_printf(DEBUG_USART, "Error: Access denied - File may be read-only\r\n");
            my_printf(DEBUG_USART, "Recommendation: Check file attributes\r\n");
            return 0; // 可以重试

        case FR_EXIST:
            my_printf(DEBUG_USART, "Error: File/directory already exists\r\n");
            my_printf(DEBUG_USART, "Recommendation: Use different name or open existing\r\n");
            return 0; // 可以重试

        case FR_INVALID_OBJECT:
            my_printf(DEBUG_USART, "Error: Invalid file/directory object\r\n");
            my_printf(DEBUG_USART, "Recommendation: Reinitialize file object\r\n");
            return 0; // 可以重试

        case FR_WRITE_PROTECTED:
            my_printf(DEBUG_USART, "Error: Write protected - TF card is locked\r\n");
            my_printf(DEBUG_USART, "Recommendation: Remove write protection\r\n");
            return 1; // 严重错误

        case FR_INVALID_DRIVE:
            my_printf(DEBUG_USART, "Error: Invalid drive number\r\n");
            my_printf(DEBUG_USART, "Recommendation: Use correct drive number (0)\r\n");
            return 1; // 严重错误

        case FR_NOT_ENABLED:
            my_printf(DEBUG_USART, "Error: Volume has no work area\r\n");
            my_printf(DEBUG_USART, "Recommendation: Mount file system first\r\n");
            return 0; // 可以重试

        case FR_NO_FILESYSTEM:
            my_printf(DEBUG_USART, "Error: No valid FAT file system\r\n");
            my_printf(DEBUG_USART, "Recommendation: Format TF card with FAT32\r\n");
            return 1; // 严重错误

        case FR_TIMEOUT:
            my_printf(DEBUG_USART, "Error: Operation timeout\r\n");
            my_printf(DEBUG_USART, "Recommendation: Retry operation\r\n");
            return 0; // 可以重试

        case FR_LOCKED:
            my_printf(DEBUG_USART, "Error: File is locked by another process\r\n");
            my_printf(DEBUG_USART, "Recommendation: Wait and retry\r\n");
            return 0; // 可以重试

        case FR_NOT_ENOUGH_CORE:
            my_printf(DEBUG_USART, "Error: Not enough memory\r\n");
            my_printf(DEBUG_USART, "Recommendation: Free memory and retry\r\n");
            return 0; // 可以重试

        case FR_TOO_MANY_OPEN_FILES:
            my_printf(DEBUG_USART, "Error: Too many open files\r\n");
            my_printf(DEBUG_USART, "Recommendation: Close unused files\r\n");
            return 0; // 可以重试

        default:
            my_printf(DEBUG_USART, "Error: Unknown error code\r\n");
            my_printf(DEBUG_USART, "Recommendation: Check FATFS documentation\r\n");
            return 1; // 未知错误，谨慎处理
    }
}

/**
 * @brief 带重试机制的TF卡操作包装函数
 * @param operation_func 要执行的操作函数指针
 * @param max_retries 最大重试次数
 * @param retry_delay_ms 重试间隔时间(毫秒)
 * @return 0-成功，其他值-失败
 */
uint8_t tf_card_operation_with_retry(uint8_t (*operation_func)(void),
                                     uint8_t max_retries,
                                     uint16_t retry_delay_ms)
{
    uint8_t result;
    uint8_t retry_count = 0;

    my_printf(DEBUG_USART, "\r\n=== TF Card Operation with Retry ===\r\n");
    my_printf(DEBUG_USART, "Max retries: %d, Delay: %d ms\r\n", max_retries, retry_delay_ms);

    do {
        if (retry_count > 0) {
            my_printf(DEBUG_USART, "Retry attempt %d/%d...\r\n", retry_count, max_retries);
            delay_ms(retry_delay_ms);
        }

        result = operation_func();

        if (result == 0) {
            my_printf(DEBUG_USART, "Operation succeeded on attempt %d\r\n", retry_count + 1);
            return 0;
        }

        my_printf(DEBUG_USART, "Operation failed with code %d\r\n", result);
        retry_count++;

    } while (retry_count <= max_retries);

    my_printf(DEBUG_USART, "Operation failed after %d attempts\r\n", retry_count);
    return result;
}

/**
 * @brief 系统集成测试函数 - 测试完整的电压采集到TF卡存储流程
 * @return 0-成功，其他值-失败
 */
uint8_t tf_card_voltage_storage_integration_test(void)
{
    my_printf(DEBUG_USART, "\r\n=== TF Card Voltage Storage Integration Test ===\r\n");

    uint8_t test_result;
    uint8_t overall_result = 0;

    // 1. TF卡硬件状态检测
    my_printf(DEBUG_USART, "\r\n--- Step 1: TF Card Hardware Test ---\r\n");
    test_result = tf_card_hardware_test();
    if (test_result != 0) {
        my_printf(DEBUG_USART, "FAILED: TF card hardware test failed with code %d\r\n", test_result);
        overall_result |= 0x01;
    } else {
        my_printf(DEBUG_USART, "PASSED: TF card hardware test\r\n");
    }

    // 2. 文件系统挂载验证
    my_printf(DEBUG_USART, "\r\n--- Step 2: File System Mount Verification ---\r\n");
    test_result = fatfs_mount_verification();
    if (test_result != 0) {
        my_printf(DEBUG_USART, "FAILED: File system mount verification failed with code %d\r\n", test_result);
        overall_result |= 0x02;
    } else {
        my_printf(DEBUG_USART, "PASSED: File system mount verification\r\n");
    }

    // 3. 目录创建功能检查
    my_printf(DEBUG_USART, "\r\n--- Step 3: Directory Creation Test ---\r\n");
    test_result = enhanced_directory_creation_test();
    if (test_result != 0) {
        my_printf(DEBUG_USART, "FAILED: Directory creation test failed with code %d\r\n", test_result);
        overall_result |= 0x04;
    } else {
        my_printf(DEBUG_USART, "PASSED: Directory creation test\r\n");
    }

    // 4. 电压读取测试
    my_printf(DEBUG_USART, "\r\n--- Step 4: Voltage Reading Test ---\r\n");
    float test_voltage = get_scaled_dac_voltage();
    if (test_voltage < 0.0f || test_voltage > 100.0f) {
        my_printf(DEBUG_USART, "FAILED: Invalid voltage reading: %.3fV\r\n", test_voltage);
        overall_result |= 0x08;
    } else {
        my_printf(DEBUG_USART, "PASSED: Voltage reading: %.3fV\r\n", test_voltage);
        my_printf(DEBUG_USART, "   Ratio: %.2f, Limit: %.2fV\r\n", g_ratio_value, g_limit_value);
    }

    // 5. 电压存储函数调试测试
    my_printf(DEBUG_USART, "\r\n--- Step 5: Voltage Storage Debug Test ---\r\n");
    test_result = debug_save_voltage_to_file(test_voltage);
    if (test_result != 0) {
        my_printf(DEBUG_USART, "FAILED: Debug voltage storage failed with code %d\r\n", test_result);
        overall_result |= 0x10;
    } else {
        my_printf(DEBUG_USART, "PASSED: Debug voltage storage\r\n");
    }

    // 6. 超限数据存储测试（如果电压超限）
    my_printf(DEBUG_USART, "\r\n--- Step 6: Over-limit Storage Test ---\r\n");
    if (test_voltage > g_limit_value) {
        test_result = save_overlimit_to_file(test_voltage);
        if (test_result != 0) {
            my_printf(DEBUG_USART, "FAILED: Over-limit storage failed with code %d\r\n", test_result);
            overall_result |= 0x20;
        } else {
            my_printf(DEBUG_USART, "PASSED: Over-limit storage\r\n");
        }
    } else {
        my_printf(DEBUG_USART, "SKIPPED: Voltage within limit (%.3fV <= %.2fV)\r\n",
                  test_voltage, g_limit_value);
    }

    // 7. 日志写入测试
    my_printf(DEBUG_USART, "\r\n--- Step 7: Log Writing Test ---\r\n");
    test_result = write_log_to_file("Integration test completed");
    if (test_result != 0) {
        my_printf(DEBUG_USART, "FAILED: Log writing failed with code %d\r\n", test_result);
        overall_result |= 0x40;
    } else {
        my_printf(DEBUG_USART, "PASSED: Log writing\r\n");
    }

    // 8. 错误处理机制测试
    my_printf(DEBUG_USART, "\r\n--- Step 8: Error Handling Test ---\r\n");
    // 测试错误处理函数
    tf_card_error_handler("Test Operation", FR_OK);
    tf_card_error_handler("Test Operation", FR_NOT_READY);
    tf_card_error_handler("Test Operation", FR_DISK_ERR);
    my_printf(DEBUG_USART, "PASSED: Error handling mechanism\r\n");

    // 9. 性能测试
    my_printf(DEBUG_USART, "\r\n--- Step 9: Performance Test ---\r\n");
    uint32_t start_time = get_system_ms();

    // 连续写入5个电压值
    for (int i = 0; i < 5; i++) {
        test_result = save_voltage_to_file(test_voltage + i * 0.1f);
        if (test_result != 0) {
            my_printf(DEBUG_USART, "FAILED: Performance test iteration %d failed\r\n", i);
            overall_result |= 0x80;
            break;
        }
    }

    uint32_t end_time = get_system_ms();
    uint32_t duration = end_time - start_time;

    if ((overall_result & 0x80) == 0) {
        my_printf(DEBUG_USART, "PASSED: Performance test - 5 writes in %d ms (avg: %d ms/write)\r\n",
                  duration, duration / 5);
    }

    // 10. 结果汇总
    my_printf(DEBUG_USART, "\r\n=== Integration Test Results Summary ===\r\n");
    my_printf(DEBUG_USART, "Test Result Bitmap: 0x%02X\r\n", overall_result);

    if (overall_result == 0) {
        my_printf(DEBUG_USART, "*** ALL TESTS PASSED ***\r\n");
        my_printf(DEBUG_USART, "TF card voltage storage system is working correctly!\r\n");
    } else {
        my_printf(DEBUG_USART, "*** SOME TESTS FAILED ***\r\n");
        my_printf(DEBUG_USART, "Failed components:\r\n");
        if (overall_result & 0x01) my_printf(DEBUG_USART, "  - TF card hardware\r\n");
        if (overall_result & 0x02) my_printf(DEBUG_USART, "  - File system mount\r\n");
        if (overall_result & 0x04) my_printf(DEBUG_USART, "  - Directory creation\r\n");
        if (overall_result & 0x08) my_printf(DEBUG_USART, "  - Voltage reading\r\n");
        if (overall_result & 0x10) my_printf(DEBUG_USART, "  - Voltage storage\r\n");
        if (overall_result & 0x20) my_printf(DEBUG_USART, "  - Over-limit storage\r\n");
        if (overall_result & 0x40) my_printf(DEBUG_USART, "  - Log writing\r\n");
        if (overall_result & 0x80) my_printf(DEBUG_USART, "  - Performance test\r\n");
    }

    my_printf(DEBUG_USART, "=== Integration Test Complete ===\r\n\r\n");
    return overall_result;
}

void adc_task(void)
{
	sd_system();
	btn_data();
    //convertarr[0] = adc_value[0];
}





