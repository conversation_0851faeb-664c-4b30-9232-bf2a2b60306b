File_name,flash percent,flash,ram,Code,RO_data,RW_data,ZI_data
c_w.l,16.124897%,12327,96,11776,551,0,96
ff.o,9.778016%,7475,518,7456,13,6,512
sdio_sdcard.o,9.379047%,7170,68,7134,0,36,32
adc_app.o,6.561409%,5016,1869,4932,51,33,1836
system.o,5.785708%,4423,625,4330,80,13,612
oled.o,5.200989%,3976,22,1242,2712,22,0
btod.o,2.815022%,2152,0,2152,0,0,0
ebtn.o,2.707758%,2070,60,2070,0,0,60
usart_app.o,2.552095%,1951,529,1802,132,17,512
gd32f470vet6.o,2.171439%,1660,592,1640,0,20,572
fz_wm.l,1.990922%,1522,0,1506,16,0,0
gd32f4xx_dma.o,1.742384%,1332,0,1332,0,0,0
scanf_fp.o,1.663898%,1272,0,1272,0,0,0
btn_app.o,1.640352%,1254,200,750,304,200,0
_printf_fp_dec.o,1.378733%,1054,0,1054,0,0,0
gd25qxx.o,1.295015%,990,0,990,0,0,0
perf_counter.o,1.263620%,966,80,882,4,80,0
_scanf.o,1.156357%,884,0,884,0,0,0
gd32f4xx_rcu.o,1.130195%,864,0,864,0,0,0
_printf_fp_hex.o,1.049093%,802,0,764,38,0,0
scanf_hexfp.o,1.046477%,800,0,800,0,0,0
m_wm.l,0.975839%,746,0,746,0,0,0
system_gd32f4xx.o,0.913051%,698,4,694,0,4,0
fatfs_unicode.o,0.902586%,690,0,474,216,0,0
gd32f4xx_adc.o,0.855495%,654,0,654,0,0,0
gd32f4xx_usart.o,0.842414%,644,0,644,0,0,0
gd32f4xx_sdio.o,0.821484%,628,0,628,0,0,0
gd32f4xx_timer.o,0.769160%,588,0,588,0,0,0
gd32f4xx_i2c.o,0.648816%,496,0,496,0,0,0
startup_gd32f450_470.o,0.643583%,492,2048,64,428,0,2048
gd32f4xx_rtc.o,0.633118%,484,0,484,0,0,0
__printf_flags_ss_wp.o,0.535011%,409,0,392,17,0,0
bigflt0.o,0.491844%,376,0,228,148,0,0
led_app.o,0.448677%,343,11,332,0,11,0
dmul.o,0.444753%,340,0,340,0,0,0
lc_ctype_c.o,0.413358%,316,0,44,272,0,0
diskio.o,0.413358%,316,0,316,0,0,0
scanf_infnan.o,0.402894%,308,0,308,0,0,0
narrow.o,0.347953%,266,0,266,0,0,0
rtc_app.o,0.346645%,265,1,264,0,1,0
gd32f4xx_gpio.o,0.342721%,262,0,262,0,0,0
lludivv7m.o,0.311327%,238,0,238,0,0,0
ldexp.o,0.298246%,228,0,228,0,0,0
gd32f4xx_misc.o,0.282549%,216,0,216,0,0,0
gd32f4xx_dac.o,0.259003%,198,0,198,0,0,0
_printf_wctomb.o,0.256387%,196,0,188,8,0,0
_printf_hex_int_ll_ptr.o,0.245922%,188,0,148,40,0,0
_printf_intcommon.o,0.232841%,178,0,178,0,0,0
scheduler.o,0.230225%,176,76,100,0,76,0
systick.o,0.219760%,168,4,164,0,4,0
gd32f4xx_it.o,0.219760%,168,0,168,0,0,0
dnaninf.o,0.204063%,156,0,156,0,0,0
perfc_port_default.o,0.201447%,154,0,154,0,0,0
strncmp.o,0.196214%,150,0,150,0,0,0
frexp.o,0.183133%,140,0,140,0,0,0
fnaninf.o,0.183133%,140,0,140,0,0,0
rt_memcpy_v6.o,0.180517%,138,0,138,0,0,0
lludiv10.o,0.180517%,138,0,138,0,0,0
main.o,0.172669%,132,0,132,0,0,0
strcmpv7m.o,0.167436%,128,0,128,0,0,0
_printf_fp_infnan.o,0.167436%,128,0,128,0,0,0
_printf_longlong_dec.o,0.162204%,124,0,124,0,0,0
dleqf.o,0.156971%,120,0,120,0,0,0
deqf.o,0.156971%,120,0,120,0,0,0
_printf_dec.o,0.156971%,120,0,120,0,0,0
_printf_oct_int_ll.o,0.146507%,112,0,112,0,0,0
drleqf.o,0.141274%,108,0,108,0,0,0
oled_app.o,0.141274%,108,0,108,0,0,0
gd32f4xx_spi.o,0.136042%,104,0,104,0,0,0
retnan.o,0.130810%,100,0,100,0,0,0
rt_memcpy_w.o,0.130810%,100,0,100,0,0,0
d2f.o,0.128193%,98,0,98,0,0,0
scalbn.o,0.120345%,92,0,92,0,0,0
__dczerorl2.o,0.117729%,90,0,90,0,0,0
memcmp.o,0.115112%,88,0,88,0,0,0
f2d.o,0.112496%,86,0,86,0,0,0
strncpy.o,0.112496%,86,0,86,0,0,0
_printf_str.o,0.107264%,82,0,82,0,0,0
rt_memclr_w.o,0.102031%,78,0,78,0,0,0
_printf_pad.o,0.102031%,78,0,78,0,0,0
sys_stackheap_outer.o,0.096799%,74,0,74,0,0,0
sd_app.o,0.096799%,74,1436,74,0,0,1436
llsdiv.o,0.094183%,72,0,72,0,0,0
lc_numeric_c.o,0.094183%,72,0,44,28,0,0
rt_memclr.o,0.088951%,68,0,68,0,0,0
dunder.o,0.083718%,64,0,64,0,0,0
_wcrtomb.o,0.083718%,64,0,64,0,0,0
_sgetc.o,0.083718%,64,0,64,0,0,0
strlen.o,0.081102%,62,0,62,0,0,0
__0sscanf.o,0.078486%,60,0,60,0,0,0
vsnprintf.o,0.068021%,52,0,52,0,0,0
__scatter.o,0.068021%,52,0,52,0,0,0
fpclassify.o,0.062789%,48,0,48,0,0,0
trapv.o,0.062789%,48,0,48,0,0,0
_printf_char_common.o,0.062789%,48,0,48,0,0,0
scanf_char.o,0.057556%,44,0,44,0,0,0
_printf_wchar.o,0.057556%,44,0,44,0,0,0
_printf_char.o,0.057556%,44,0,44,0,0,0
__2sprintf.o,0.057556%,44,0,44,0,0,0
_printf_charcount.o,0.052324%,40,0,40,0,0,0
llshl.o,0.049708%,38,0,38,0,0,0
libinit2.o,0.049708%,38,0,38,0,0,0
strstr.o,0.047091%,36,0,36,0,0,0
init_aeabi.o,0.047091%,36,0,36,0,0,0
_printf_truncate.o,0.047091%,36,0,36,0,0,0
systick_wrapper_ual.o,0.041859%,32,0,32,0,0,0
_chval.o,0.036627%,28,0,28,0,0,0
__scatter_zi.o,0.036627%,28,0,28,0,0,0
dcmpi.o,0.031394%,24,0,24,0,0,0
strcat.o,0.031394%,24,0,24,0,0,0
_rserrno.o,0.028778%,22,0,22,0,0,0
gd32f4xx_pmu.o,0.026162%,20,0,20,0,0,0
isspace.o,0.023546%,18,0,18,0,0,0
exit.o,0.023546%,18,0,18,0,0,0
fpconst.o,0.020930%,16,0,0,16,0,0
dcheck1.o,0.020930%,16,0,16,0,0,0
rt_ctype_table.o,0.020930%,16,0,16,0,0,0
_snputc.o,0.020930%,16,0,16,0,0,0
__printf_wp.o,0.018313%,14,0,14,0,0,0
dretinf.o,0.015697%,12,0,12,0,0,0
sys_exit.o,0.015697%,12,0,12,0,0,0
__rtentry2.o,0.015697%,12,0,12,0,0,0
fretinf.o,0.013081%,10,0,10,0,0,0
fpinit.o,0.013081%,10,0,10,0,0,0
rtexit2.o,0.013081%,10,0,10,0,0,0
_sputc.o,0.013081%,10,0,10,0,0,0
_printf_ll.o,0.013081%,10,0,10,0,0,0
_printf_l.o,0.013081%,10,0,10,0,0,0
scanf2.o,0.010465%,8,0,8,0,0,0
rt_locale_intlibspace.o,0.010465%,8,0,8,0,0,0
rt_errno_addr_intlibspace.o,0.010465%,8,0,8,0,0,0
libspace.o,0.010465%,8,96,8,0,0,96
__main.o,0.010465%,8,0,8,0,0,0
istatus.o,0.007849%,6,0,6,0,0,0
heapauxi.o,0.007849%,6,0,6,0,0,0
_printf_x.o,0.007849%,6,0,6,0,0,0
_printf_u.o,0.007849%,6,0,6,0,0,0
_printf_s.o,0.007849%,6,0,6,0,0,0
_printf_p.o,0.007849%,6,0,6,0,0,0
_printf_o.o,0.007849%,6,0,6,0,0,0
_printf_n.o,0.007849%,6,0,6,0,0,0
_printf_ls.o,0.007849%,6,0,6,0,0,0
_printf_llx.o,0.007849%,6,0,6,0,0,0
_printf_llu.o,0.007849%,6,0,6,0,0,0
_printf_llo.o,0.007849%,6,0,6,0,0,0
_printf_lli.o,0.007849%,6,0,6,0,0,0
_printf_lld.o,0.007849%,6,0,6,0,0,0
_printf_lc.o,0.007849%,6,0,6,0,0,0
_printf_i.o,0.007849%,6,0,6,0,0,0
_printf_g.o,0.007849%,6,0,6,0,0,0
_printf_f.o,0.007849%,6,0,6,0,0,0
_printf_e.o,0.007849%,6,0,6,0,0,0
_printf_d.o,0.007849%,6,0,6,0,0,0
_printf_c.o,0.007849%,6,0,6,0,0,0
_printf_a.o,0.007849%,6,0,6,0,0,0
__rtentry4.o,0.007849%,6,0,6,0,0,0
scanf1.o,0.005232%,4,0,4,0,0,0
printf2.o,0.005232%,4,0,4,0,0,0
printf1.o,0.005232%,4,0,4,0,0,0
_printf_percent_end.o,0.005232%,4,0,4,0,0,0
use_no_semi.o,0.002616%,2,0,2,0,0,0
rtexit.o,0.002616%,2,0,2,0,0,0
libshutdown2.o,0.002616%,2,0,2,0,0,0
libshutdown.o,0.002616%,2,0,2,0,0,0
libinit.o,0.002616%,2,0,2,0,0,0
